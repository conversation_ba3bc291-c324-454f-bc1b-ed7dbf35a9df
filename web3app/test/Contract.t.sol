// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../src/Crowdfunding.sol";

contract CrowdfundingTest is Test {
    Crowdfunding public crowdfunding;
    address public owner = address(0x123);
    address public backer1 = address(0x456);
    address public backer2 = address(0x789);

    uint256 public constant GOAL = 10 ether;
    uint256 public constant DURATION = 30; // 30 days

    function setUp() public {
        vm.prank(owner);
        crowdfunding = new Crowdfunding(
            owner,
            "Test Campaign",
            "A test crowdfunding campaign",
            GOAL,
            DURATION
        );
    }

    function testInitialState() public view {
        assertEq(crowdfunding.name(), "Test Campaign");
        assertEq(crowdfunding.description(), "A test crowdfunding campaign");
        assertEq(crowdfunding.goal(), GOAL);
        assertEq(crowdfunding.owner(), owner);
        assertEq(uint(crowdfunding.state()), uint(Crowdfunding.CampaignState.Active));
        assertFalse(crowdfunding.paused());
    }

    function testAddTier() public {
        vm.prank(owner);
        crowdfunding.addTier("Bronze", 1 ether);

        Crowdfunding.Tier[] memory tiers = crowdfunding.getTiers();
        assertEq(tiers.length, 1);
        assertEq(tiers[0].name, "Bronze");
        assertEq(tiers[0].amount, 1 ether);
        assertEq(tiers[0].backers, 0);
    }

    function testFunding() public {
        // Add a tier first
        vm.prank(owner);
        crowdfunding.addTier("Bronze", 1 ether);

        // Fund the tier
        vm.deal(backer1, 5 ether);
        vm.prank(backer1);
        crowdfunding.fund{value: 1 ether}(0);

        // Check the funding was successful
        assertEq(crowdfunding.getContractBalance(), 1 ether);
        assertTrue(crowdfunding.hasFundedTier(backer1, 0));

        Crowdfunding.Tier[] memory tiers = crowdfunding.getTiers();
        assertEq(tiers[0].backers, 1);
    }

    function test_RevertWhen_FundingWithIncorrectAmount() public {
        vm.prank(owner);
        crowdfunding.addTier("Bronze", 1 ether);

        vm.deal(backer1, 5 ether);
        vm.prank(backer1);
        // This should fail because we're sending 0.5 ether instead of 1 ether
        vm.expectRevert("Incorrect amount.");
        crowdfunding.fund{value: 0.5 ether}(0);
    }

    function testSuccessfulCampaign() public {
        // Add tiers
        vm.prank(owner);
        crowdfunding.addTier("Bronze", 5 ether);
        vm.prank(owner);
        crowdfunding.addTier("Silver", 5 ether);

        // Fund enough to reach the goal
        vm.deal(backer1, 10 ether);
        vm.deal(backer2, 10 ether);

        vm.prank(backer1);
        crowdfunding.fund{value: 5 ether}(0);

        vm.prank(backer2);
        crowdfunding.fund{value: 5 ether}(1);

        // Check campaign is successful
        assertEq(uint(crowdfunding.getCampaignStatus()), uint(Crowdfunding.CampaignState.Successful));
    }

    function testWithdrawAfterSuccess() public {
        // Setup successful campaign
        vm.prank(owner);
        crowdfunding.addTier("Bronze", 10 ether);

        vm.deal(backer1, 15 ether);
        vm.prank(backer1);
        crowdfunding.fund{value: 10 ether}(0);

        // Give owner some initial balance
        vm.deal(owner, 1 ether);

        // Owner should be able to withdraw
        uint256 ownerBalanceBefore = owner.balance;
        vm.prank(owner);
        crowdfunding.withdraw();

        assertEq(owner.balance, ownerBalanceBefore + 10 ether);
        assertEq(crowdfunding.getContractBalance(), 0);
    }
}
