{"name": "forge-starter", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"node_modules/@openzeppelin/contracts": {"version": "4.9.6", "resolved": "https://registry.npmjs.org/@openzeppelin/contracts/-/contracts-4.9.6.tgz", "integrity": "sha512-xSmezSupL+y9VkHZJGDoCBpmnB2ogM13ccaYDWqJTfS3dbuHkgjuwDFUmaFauBCboQMGB/S5UqUl2y54X99BmA==", "license": "MIT"}, "node_modules/@openzeppelin/contracts-upgradeable": {"version": "4.9.6", "resolved": "https://registry.npmjs.org/@openzeppelin/contracts-upgradeable/-/contracts-upgradeable-4.9.6.tgz", "integrity": "sha512-m4iHazOsOCv1DgM7eD7GupTJ+NFVujRZt1wzddDPSVGpWdKq1SKkla5htKG7+IS4d2XOCtzkUNwRZ7Vq5aEUMA==", "license": "MIT"}, "node_modules/@thirdweb-dev/contracts": {"version": "3.15.0", "resolved": "https://registry.npmjs.org/@thirdweb-dev/contracts/-/contracts-3.15.0.tgz", "integrity": "sha512-sIXPy6zNqW9K9h8xgCnsRQVrqmmMdxoDqut4eAZj1CJzMax5TyrNBoSYCfAX0et8KApvBeCVeJQhUHSWpBNIVw==", "license": "Apache-2.0", "dependencies": {"@openzeppelin/contracts": "^4.9.3", "@openzeppelin/contracts-upgradeable": "^4.9.3", "@thirdweb-dev/dynamic-contracts": "^1.2.4", "erc721a-upgradeable": "^3.3.0", "solady": "0.0.180"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@thirdweb-dev/dynamic-contracts": {"version": "1.2.5", "resolved": "https://registry.npmjs.org/@thirdweb-dev/dynamic-contracts/-/dynamic-contracts-1.2.5.tgz", "integrity": "sha512-YVsz+jUWbwj+6aF2eTZGMfyw47a1HRmgNl4LQ3gW9gwYL5y5+OX/yOzv6aV5ibvoqCk/k10aIVK2eFrcpMubQA==", "license": "MIT", "engines": {"node": ">=18.0.0"}}, "node_modules/erc721a-upgradeable": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/erc721a-upgradeable/-/erc721a-upgradeable-3.3.0.tgz", "integrity": "sha512-ILE0SjKuvhx+PABG0A/41QUp0MFiYmzrgo71htQ0Ov6JfDOmgUzGxDW8gZuYfKrdlYjNwSAqMpUFWBbyW3sWBA==", "license": "ISC", "dependencies": {"@openzeppelin/contracts-upgradeable": "^4.4.2"}}, "node_modules/solady": {"version": "0.0.180", "resolved": "https://registry.npmjs.org/solady/-/solady-0.0.180.tgz", "integrity": "sha512-9QVCyMph+wk78Aq/GxtDAQg7dvNoVWx2dS2Zwf11XlwFKDZ+YJG2lrQsK9NEIth9NOebwjBXAYk4itdwOOE4aw==", "license": "MIT"}}}