{"abi": [], "bytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "deployedBytecode": {"object": "0x", "sourceMap": "", "linkReferences": {}}, "methodIdentifiers": {}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.30+commit.73712a01\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"notice\":\"StdChai<PERSON> provides information about EVM compatible chains that can be used in scripts/tests. For each chain, the chain's name, chain ID, and a default RPC URL are provided. Chains are identified by their alias, which is the same as the alias in the `[rpc_endpoints]` section of the `foundry.toml` file. For best UX, ensure the alias in the `foundry.toml` file match the alias used in this contract, which can be found as the first argument to the `setChainWithDefaultRpcUrl` call in the `initializeStdChains` function. There are two main ways to use this contract:   1. Set a chain with `setChain(string memory chainAlias, ChainData memory chain)` or      `setChain(string memory chainAlias, Chain memory chain)`   2. Get a chain with `get<PERSON>hai<PERSON>(string memory chainAlias)` or `get<PERSON>hain(uint256 chainId)`. The first time either of those are used, chains are initialized with the default set of RPC URLs. This is done in `initializeStdChains`, which uses `setChainWithDefaultRpcUrl`. Defaults are recorded in `defaultRpcUrls`. The `setChain` function is straightforward, and it simply saves off the given chain data. The `getChain` methods use `getChainWithUpdatedRpcUrl` to return a chain. For example, let's say we want to retrieve the RPC URL for `mainnet`:   - If you have specified data with `setChain`, it will return that.   - If you have configured a mainnet RPC URL in `foundry.toml`, it will return the URL, provided it     is valid (e.g. a URL is specified, or an environment variable is given and exists).   - If neither of the above conditions is met, the default data is returned. Summarizing the above, the prioritization hierarchy is `setChain` -> `foundry.toml` -> environment variable -> defaults.\",\"version\":1}},\"settings\":{\"compilationTarget\":{\"lib/forge-std/src/StdChains.sol\":\"StdChains\"},\"evmVersion\":\"cancun\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[\":@thirdweb-dev/=node_modules/@thirdweb-dev/\",\":forge-std/=lib/forge-std/src/\"]},\"sources\":{\"lib/forge-std/src/StdChains.sol\":{\"keccak256\":\"0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351\",\"dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm\"]},\"lib/forge-std/src/Vm.sol\":{\"keccak256\":\"0xe2b159412b06b44a7f90972104300c587f308506d37d2143dd7e689e2eac6f01\",\"license\":\"MIT OR Apache-2.0\",\"urls\":[\"bzz-raw://a96e13ac6fec3ffe61a55d6005ef0ef96f47adf3f4f3531e0418f0bf02d3f93c\",\"dweb:/ipfs/QmeFci69gm7a6c6pEqSNoe3HfXPVPiagUc51Pyo5PoS8Rn\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.30+commit.73712a01"}, "language": "Solidity", "output": {"abi": [], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@thirdweb-dev/=node_modules/@thirdweb-dev/", "forge-std/=lib/forge-std/src/"], "optimizer": {"enabled": false, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"lib/forge-std/src/StdChains.sol": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "evmVersion": "cancun", "libraries": {}}, "sources": {"lib/forge-std/src/StdChains.sol": {"keccak256": "0xd3edaf57db178f839d97ccee0009c2b7b20f78c2adca9028a6013acb8d5b4c5e", "urls": ["bzz-raw://0f17329ac1d25d3e6657ca240dee0a3f9b2cf22a64a05b87dac15252dee74351", "dweb:/ipfs/QmPHxkEwgVpQNbU52s58RmmegrkYGj8bNKTVSW8rZm3dBm"], "license": "MIT"}, "lib/forge-std/src/Vm.sol": {"keccak256": "0xe2b159412b06b44a7f90972104300c587f308506d37d2143dd7e689e2eac6f01", "urls": ["bzz-raw://a96e13ac6fec3ffe61a55d6005ef0ef96f47adf3f4f3531e0418f0bf02d3f93c", "dweb:/ipfs/QmeFci69gm7a6c6pEqSNoe3HfXPVPiagUc51Pyo5PoS8Rn"], "license": "MIT OR Apache-2.0"}}, "version": 1}, "id": 2}