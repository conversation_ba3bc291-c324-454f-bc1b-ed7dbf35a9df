// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract Crowdfunding {
    string public name;
    string public description;
    uint256 public goal;
    uint256 public deadline;
    address public owner;
    bool public paused;

    // Events
    event FundReceived(address indexed backer, uint256 amount, uint256 tierIndex);
    event TierAdded(string name, uint256 amount);
    event TierRemoved(uint256 index);
    event CampaignStateChanged(CampaignState newState);
    event RefundIssued(address indexed backer, uint256 amount);
    event FundsWithdrawn(address indexed owner, uint256 amount);
    event CampaignPaused(bool paused);
    event DeadlineExtended(uint256 newDeadline);

    enum CampaignState { Active, Successful, Failed }
    CampaignState public state;

    struct Tier {
        string name;
        uint256 amount;
        uint256 backers;
    }

    struct Backer {
        uint256 totalContribution;
        mapping(uint256 => bool) fundedTiers;
    }

    Tier[] public tiers;
    mapping(address => Backer) public backers;

    modifier onlyOwner() {
        require(msg.sender == owner, "Not the owner");
        _;
    }

    modifier campaignOpen() {
        require(state == CampaignState.Active, "Campaign is not active.");
        _;
    }

    modifier notPaused() {
        require(!paused, "Contract is paused.");
        _;
    }
    
    constructor(
        address _owner,
        string memory _name,
        string memory _description,
        uint256 _goal,
        uint256 _durationInDays
    ) {
        require(_owner != address(0), "Owner cannot be zero address");
        require(bytes(_name).length > 0, "Name cannot be empty");
        require(bytes(_description).length > 0, "Description cannot be empty");
        require(_goal > 0, "Goal must be greater than 0");
        require(_durationInDays > 0, "Duration must be greater than 0");

        name = _name;
        description = _description;
        goal = _goal;
        deadline = block.timestamp + (_durationInDays * 1 days);
        owner = _owner;
        state = CampaignState.Active;
    }

    function checkAndUpdateCampaignState() internal {
        if(state == CampaignState.Active) {
            CampaignState newState;
            if(block.timestamp >= deadline) {
                newState = address(this).balance >= goal ? CampaignState.Successful : CampaignState.Failed;
            } else {
                newState = address(this).balance >= goal ? CampaignState.Successful : CampaignState.Active;
            }

            if (newState != state) {
                state = newState;
                emit CampaignStateChanged(newState);
            }
        }
    }

    function fund(uint256 _tierIndex) public payable campaignOpen notPaused {
        require(_tierIndex < tiers.length, "Invalid tier.");
        require(msg.value == tiers[_tierIndex].amount, "Incorrect amount.");

        tiers[_tierIndex].backers++;
        backers[msg.sender].totalContribution += msg.value;
        backers[msg.sender].fundedTiers[_tierIndex] = true;

        emit FundReceived(msg.sender, msg.value, _tierIndex);
        checkAndUpdateCampaignState();
    }

    function addTier(
        string memory _name,
        uint256 _amount
    ) public onlyOwner {
        require(_amount > 0, "Amount must be greater than 0.");
        require(bytes(_name).length > 0, "Tier name cannot be empty");
        tiers.push(Tier(_name, _amount, 0));
        emit TierAdded(_name, _amount);
    }

    function removeTier(uint256 _index) public onlyOwner {
        require(_index < tiers.length, "Tier does not exist.");
        tiers[_index] = tiers[tiers.length -1];
        tiers.pop();
        emit TierRemoved(_index);
    }

    function withdraw() public onlyOwner {
        checkAndUpdateCampaignState();
        require(state == CampaignState.Successful, "Campaign not successful.");

        uint256 balance = address(this).balance;
        require(balance > 0, "No balance to withdraw");

        payable(owner).transfer(balance);
        emit FundsWithdrawn(owner, balance);
    }

    function getContractBalance() public view returns (uint256) {
        return address(this).balance;
    }

    function getTotalRaised() public view returns (uint256) {
        return address(this).balance;
    }

    function refund() public {
        checkAndUpdateCampaignState();
        require(state == CampaignState.Failed, "Refunds not available.");
        uint256 amount = backers[msg.sender].totalContribution;
        require(amount > 0, "No contribution to refund");

        // Clear the contribution before transferring to prevent reentrancy
        backers[msg.sender].totalContribution = 0;

        // Clear all funded tiers for this backer
        for (uint256 i = 0; i < tiers.length; i++) {
            if (backers[msg.sender].fundedTiers[i]) {
                backers[msg.sender].fundedTiers[i] = false;
                tiers[i].backers--;
            }
        }

        payable(msg.sender).transfer(amount);
        emit RefundIssued(msg.sender, amount);
    }

    function hasFundedTier(address _backer, uint256 _tierIndex) public view returns (bool) {
        return backers[_backer].fundedTiers[_tierIndex];
    }

    function getTiers() public view returns (Tier[] memory) {
        return tiers;
    }

    function togglePause() public onlyOwner {
        paused = !paused;
        emit CampaignPaused(paused);
    }

    function getCampaignStatus() public view returns (CampaignState) {
        if (state == CampaignState.Active && block.timestamp > deadline) {
            return address(this).balance >= goal ? CampaignState.Successful : CampaignState.Failed;
        }
        return state;
    }

    function extendDeadline(uint256 _daysToAdd) public onlyOwner campaignOpen {
        require(_daysToAdd > 0, "Days to add must be greater than 0");
        deadline += _daysToAdd * 1 days;
        emit DeadlineExtended(deadline);
    }
}