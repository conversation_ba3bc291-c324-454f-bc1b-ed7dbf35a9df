{"_format": "", "paths": {"artifacts": "out", "build_infos": "out/build-info", "sources": "src", "tests": "test", "scripts": "script", "libraries": ["lib"]}, "files": {"lib/forge-std/src/Base.sol": {"lastModificationDate": 1754805006341, "contentHash": "b30affbf365427e2", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/Base.sol", "imports": ["lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"CommonBase": {"0.8.30": {"default": {"path": "Base.sol/CommonBase.json", "build_id": "956f993a8ff69132"}}}, "ScriptBase": {"0.8.30": {"default": {"path": "Base.sol/ScriptBase.json", "build_id": "956f993a8ff69132"}}}, "TestBase": {"0.8.30": {"default": {"path": "Base.sol/TestBase.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdAssertions.sol": {"lastModificationDate": 1754805006341, "contentHash": "02aafa55c6c27fcf", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdAssertions.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdAssertions": {"0.8.30": {"default": {"path": "StdAssertions.sol/StdAssertions.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdChains.sol": {"lastModificationDate": 1754805006341, "contentHash": "a40952ce0d242817", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdChains.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdChains": {"0.8.30": {"default": {"path": "StdChains.sol/StdChains.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdCheats.sol": {"lastModificationDate": 1754805006341, "contentHash": "30325e8cda32c7ae", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdCheats.sol", "imports": ["lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdCheats": {"0.8.30": {"default": {"path": "StdCheats.sol/StdCheats.json", "build_id": "956f993a8ff69132"}}}, "StdCheatsSafe": {"0.8.30": {"default": {"path": "StdCheats.sol/StdCheatsSafe.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdConstants.sol": {"lastModificationDate": 1754805006341, "contentHash": "23303eb7e922efe4", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdConstants.sol", "imports": ["lib/forge-std/src/Vm.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdConstants": {"0.8.30": {"default": {"path": "StdConstants.sol/StdConstants.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdError.sol": {"lastModificationDate": 1754805006341, "contentHash": "a1a86c7115e2cdf3", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdError.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdError": {"0.8.30": {"default": {"path": "StdError.sol/stdError.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdInvariant.sol": {"lastModificationDate": 1754805006341, "contentHash": "0111ef959dff6f54", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdInvariant.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdInvariant": {"0.8.30": {"default": {"path": "StdInvariant.sol/StdInvariant.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdJson.sol": {"lastModificationDate": 1754805006341, "contentHash": "5fb1b35c8fb281fd", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdJson.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.0, <0.9.0", "artifacts": {"stdJson": {"0.8.30": {"default": {"path": "StdJson.sol/stdJson.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdMath.sol": {"lastModificationDate": 1754805006341, "contentHash": "72584abebada1e7a", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdMath.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdMath": {"0.8.30": {"default": {"path": "StdMath.sol/stdMath.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdStorage.sol": {"lastModificationDate": 1754805006345, "contentHash": "9a44dcb9bda3bfa9", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdStorage.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"stdStorage": {"0.8.30": {"default": {"path": "StdStorage.sol/stdStorage.json", "build_id": "956f993a8ff69132"}}}, "stdStorageSafe": {"0.8.30": {"default": {"path": "StdStorage.sol/stdStorageSafe.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdStyle.sol": {"lastModificationDate": 1754805006345, "contentHash": "ee166ef95092736e", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdStyle.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"StdStyle": {"0.8.30": {"default": {"path": "StdStyle.sol/StdStyle.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdToml.sol": {"lastModificationDate": 1754805006345, "contentHash": "fc667e4ecb7fa86c", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdToml.sol", "imports": ["lib/forge-std/src/Vm.sol"], "versionRequirement": ">=0.6.0, <0.9.0", "artifacts": {"stdToml": {"0.8.30": {"default": {"path": "StdToml.sol/stdToml.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/StdUtils.sol": {"lastModificationDate": 1754805006345, "contentHash": "b7cdeb66252de708", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/StdUtils.sol", "imports": ["lib/forge-std/src/Vm.sol", "lib/forge-std/src/interfaces/IMulticall3.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"StdUtils": {"0.8.30": {"default": {"path": "StdUtils.sol/StdUtils.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/Test.sol": {"lastModificationDate": 1754805006345, "contentHash": "f56119a09f81c62c", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/Test.sol", "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol"], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Test": {"0.8.30": {"default": {"path": "Test.sol/Test.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/Vm.sol": {"lastModificationDate": 1754805006345, "contentHash": "e42237c90542cb12", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/Vm.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"Vm": {"0.8.30": {"default": {"path": "Vm.sol/Vm.json", "build_id": "956f993a8ff69132"}}}, "VmSafe": {"0.8.30": {"default": {"path": "Vm.sol/VmSafe.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/console.sol": {"lastModificationDate": 1754805006345, "contentHash": "bae85493a76fb054", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/console.sol", "imports": [], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {"console": {"0.8.30": {"default": {"path": "console.sol/console.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/console2.sol": {"lastModificationDate": 1754805006345, "contentHash": "49a7da3dfc404603", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/console2.sol", "imports": ["lib/forge-std/src/console.sol"], "versionRequirement": ">=0.4.22, <0.9.0", "artifacts": {}, "seenByCompiler": true}, "lib/forge-std/src/interfaces/IMulticall3.sol": {"lastModificationDate": 1754805006345, "contentHash": "b680a332ebf10901", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/interfaces/IMulticall3.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"IMulticall3": {"0.8.30": {"default": {"path": "IMulticall3.sol/IMulticall3.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "lib/forge-std/src/safeconsole.sol": {"lastModificationDate": 1754805006345, "contentHash": "621653b34a6691ea", "interfaceReprHash": null, "sourceName": "lib/forge-std/src/safeconsole.sol", "imports": [], "versionRequirement": ">=0.6.2, <0.9.0", "artifacts": {"safeconsole": {"0.8.30": {"default": {"path": "safeconsole.sol/safeconsole.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "src/Crowdfunding.sol": {"lastModificationDate": 1754807234817, "contentHash": "a6e8afd1f38bdbb2", "interfaceReprHash": null, "sourceName": "src/Crowdfunding.sol", "imports": [], "versionRequirement": "^0.8.0", "artifacts": {"Crowdfunding": {"0.8.30": {"default": {"path": "Crowdfunding.sol/Crowdfunding.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "src/CrowdfundingFactory.sol": {"lastModificationDate": 1754805231969, "contentHash": "282160b18aa7921d", "interfaceReprHash": null, "sourceName": "src/CrowdfundingFactory.sol", "imports": ["src/Crowdfunding.sol"], "versionRequirement": "^0.8.0", "artifacts": {"CrowdfundingFactory": {"0.8.30": {"default": {"path": "CrowdfundingFactory.sol/CrowdfundingFactory.json", "build_id": "956f993a8ff69132"}}}}, "seenByCompiler": true}, "test/Contract.t.sol": {"lastModificationDate": 1754807582192, "contentHash": "40c60bd9e2f8b517", "interfaceReprHash": null, "sourceName": "test/Contract.t.sol", "imports": ["lib/forge-std/src/Base.sol", "lib/forge-std/src/StdAssertions.sol", "lib/forge-std/src/StdChains.sol", "lib/forge-std/src/StdCheats.sol", "lib/forge-std/src/StdConstants.sol", "lib/forge-std/src/StdError.sol", "lib/forge-std/src/StdInvariant.sol", "lib/forge-std/src/StdJson.sol", "lib/forge-std/src/StdMath.sol", "lib/forge-std/src/StdStorage.sol", "lib/forge-std/src/StdStyle.sol", "lib/forge-std/src/StdToml.sol", "lib/forge-std/src/StdUtils.sol", "lib/forge-std/src/Test.sol", "lib/forge-std/src/Vm.sol", "lib/forge-std/src/console.sol", "lib/forge-std/src/console2.sol", "lib/forge-std/src/interfaces/IMulticall3.sol", "lib/forge-std/src/safeconsole.sol", "src/Crowdfunding.sol"], "versionRequirement": "^0.8.0", "artifacts": {"CrowdfundingTest": {"0.8.30": {"default": {"path": "Contract.t.sol/CrowdfundingTest.json", "build_id": "1dc42df39530b895"}}}}, "seenByCompiler": true}}, "builds": ["1dc42df39530b895", "956f993a8ff69132"], "profiles": {"default": {"solc": {"optimizer": {"enabled": false, "runs": 200}, "metadata": {"useLiteralContent": false, "bytecodeHash": "ipfs", "appendCBOR": true}, "outputSelection": {"*": {"*": ["abi", "evm.bytecode.object", "evm.bytecode.sourceMap", "evm.bytecode.linkReferences", "evm.deployedBytecode.object", "evm.deployedBytecode.sourceMap", "evm.deployedBytecode.linkReferences", "evm.deployedBytecode.immutableReferences", "evm.methodIdentifiers", "metadata"]}}, "evmVersion": "cancun", "viaIR": false, "libraries": {}}, "vyper": {"evmVersion": "cancun", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode"]}}}}}, "preprocessed": false, "mocks": []}