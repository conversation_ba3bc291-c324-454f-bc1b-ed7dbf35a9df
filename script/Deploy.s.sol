// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Script.sol";
import "../src/Crowdfunding.sol";

contract DeployScript is Script {
    function run() external {
        // Use the first test account from anvil
        uint256 deployerPrivateKey = 0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80;
        
        vm.startBroadcast(deployerPrivateKey);
        
        // Deploy the crowdfunding contract
        Crowdfunding crowdfunding = new Crowdfunding(
            vm.addr(deployerPrivateKey), // owner
            "My First Crowdfunding Campaign",
            "This is a test crowdfunding campaign for learning purposes",
            5 ether, // goal: 5 ETH
            30 // duration: 30 days
        );
        
        console.log("Crowdfunding contract deployed at:", address(crowdfunding));
        console.log("Owner:", crowdfunding.owner());
        console.log("Goal:", crowdfunding.goal());
        console.log("Name:", crowdfunding.name());
        
        // Add some tiers
        crowdfunding.addTier("Bronze Supporter", 0.1 ether);
        crowdfunding.addTier("Silver Supporter", 0.5 ether);
        crowdfunding.addTier("Gold Supporter", 1 ether);
        crowdfunding.addTier("Platinum Supporter", 2 ether);
        
        console.log("Added 4 funding tiers");
        
        vm.stopBroadcast();
    }
}
